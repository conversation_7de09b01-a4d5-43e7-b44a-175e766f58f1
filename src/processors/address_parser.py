from abc import ABC, abstractmethod

import orjson
import pandas as pd
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_result

from src.data_structures.parsed_address import ParsedAddress
from src.facades.address_map_call import call_address_ner, call_address_ner_batch
from src.utils.logger import logger
from src.utils.prometheus import track_address_division_parsing


def _has_missing_admin_divisions(parsed_address: ParsedAddress | None) -> bool:
    """
    Check if the parsed address is missing all admin division fields.

    Args:
        parsed_address: The parsed address result or None

    Returns:
        True if all admin divisions (state, city, district) are missing or empty, False otherwise
    """
    if parsed_address is None:
        return True

    return not (parsed_address.state or parsed_address.city or parsed_address.district)


class AddressParser(ABC):
    @abstractmethod
    def parse(self, address: str) -> ParsedAddress:
        pass

    @abstractmethod
    def parse_in_batch(self, address_df: pd.DataFrame) -> pd.DataFrame:
        pass


class NERAddressParser(AddressParser):
    def __init__(self, region: str = 'VN'):
        self.region = region

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=16),
        retry=retry_if_result(_has_missing_admin_divisions)
    )
    def parse(self, address: str) -> ParsedAddress | None:
        address_df = pd.DataFrame({
            'buyer_shipping_address': [address],
            # Empty as NER API will parse these
            'buyer_shipping_address_state': [''],
            'buyer_shipping_address_city': [''],
            'buyer_shipping_address_district': ['']
        })

        processed_df = call_address_ner_batch(address_df, self.region)

        if processed_df.empty or processed_df.freetext_formatted_address.iloc[0] == '':
            logger.warning(
                f'NER processing failed or returned empty result {address}')
            return None

        address_segments = orjson.loads(processed_df.address_segment.iloc[0])

        parsed_address = ParsedAddress(
            address=address,
            formatted_address=processed_df.formatted_address.iloc[0],
            freetext_formatted_address=processed_df.freetext_formatted_address.iloc[0],
            state=address_segments.get('admin_division_1', ''),
            city=address_segments.get('admin_division_2', ''),
            district=address_segments.get('admin_division_3', ''),
        )

        # Track division info metrics
        has_division_info = bool(parsed_address.state or parsed_address.city or parsed_address.district)
        track_address_division_parsing(has_division_info)

        # Log if admin divisions are missing for retry tracking
        if not has_division_info:
            logger.warning(f'Address parsing returned empty admin divisions for: {address[:50]}...')

        return parsed_address

    def parse_in_batch(self, address_df: pd.DataFrame) -> pd.DataFrame:
        logger.info(f'Starting address parsing...')

        # Validate and prepare input
        required_cols = ['delivery_address']
        for col in required_cols:
            if col not in address_df.columns:
                raise ValueError(f"Required column '{col}' not found in input")

        # Rename delivery_address to buyer_shipping_address to satisfy the util function.
        address_df.rename(columns={'delivery_address': 'buyer_shipping_address'}, inplace=True)

        # Empty as NER API will parse
        address_df['buyer_shipping_address_state'] = ''
        address_df['buyer_shipping_address_city'] = ''
        address_df['buyer_shipping_address_district'] = ''

        processed_df = call_address_ner(address_df, self.region)

        # Process address segments for each row
        def extract_address_segments(segment_str):
            try:
                segments = orjson.loads(segment_str)
                return pd.Series({
                    'state': segments.get('admin_division_1', ''),
                    'city': segments.get('admin_division_2', ''),
                    'district': segments.get('admin_division_3', '')
                })
            except (orjson.JSONDecodeError, TypeError) as e:
                logger.warning(f"Error parsing address segment: {e}")
                return pd.Series({'state': '', 'city': '', 'district': ''})

        # Apply the extraction to each row
        address_segments_df = processed_df['address_segment'].apply(extract_address_segments)
        processed_df[['state', 'city', 'district']] = address_segments_df
        processed_df['grass_region'] = self.region

        # Track division info metrics for all processed addresses
        for _, row in processed_df.iterrows():
            has_division_info = bool(row['state'] or row['city'] or row['district'])
            track_address_division_parsing(has_division_info)

        # Remove the rows that has empty state, city, district
        processed_df = processed_df[(processed_df['state'] != '') |
                                    (processed_df['city'] != '') | (processed_df['district'] != '')]

        # Remove the rows that has empty formatted_address
        processed_df = processed_df[processed_df['freetext_formatted_address'] != '']

        return processed_df
