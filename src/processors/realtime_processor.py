import json
import time
from collections import deque
from threading import Thread, Lock, Event

import orjson
from confluent_kafka import Consumer, Message, KafkaError, OFFSET_END

from src.data_structures.observe_notify import INotifier
from src.data_structures.request import Request
from src.exporters.base import Exporter
from src.facades.redis import redis_facade
from src.processors.address_clusterer import AddressClusterer
from src.utils.file import write_to_file
from src.utils.logger import logger
from src.common.consts import PROCESS_REQUESTS_WITHIN_SECONDS, LOG_PATH


class RealtimeProcessor(INotifier):
    def __init__(self,
                 address_clusterer: AddressClusterer,
                 consumer: Consumer,
                 filtered_order_topic: str,
                 max_queue_size: int = 100):
        self.address_clusterer = address_clusterer
        self.filtered_order_topic = filtered_order_topic
        self.observers: list[Exporter] = []

        # Initialize Kafka consumer
        self.consumer = consumer
        self.consumer.subscribe([filtered_order_topic], on_assign=self._on_consumer_partition_assign)

        # Initialize request queue and locks
        self.requests: deque[tuple[Message, Request]] = deque(maxlen=max_queue_size)
        self.lock = Lock()
        self.running = Event()

        # Initialize threads
        self.consumer_thread = Thread(target=self._consume_messages, daemon=True)
        self.processor_thread = Thread(target=self._process_requests, daemon=True)

    @staticmethod
    def _on_consumer_partition_assign(consumer: Consumer, partitions):
        if len(partitions) > 0:
            logger.info(f"RealtimeProcessor consumer group rebalance, consumer: {consumer.memberid()}, "
                        f"partitions: {[p.partition for p in partitions]}")

            write_to_file(f'{LOG_PATH}/ready.log', "ok")

    def _consume_messages(self):
        """Consume messages from Kafka and add them to the request queue."""
        logger.info("RealtimeProcessor started consuming messages...")

        while self.running.is_set():
            try:
                msg = self.consumer.poll(timeout=0.1)
                if msg is None:
                    continue
                if msg.error():
                    if msg.error().fatal() or msg.error().code() == KafkaError._FATAL:
                        logger.error(f"Fatal Kafka error: {msg.error()}")
                        self._reinit_consumer()
                    continue

                try:
                    request_data = orjson.loads(msg.value().decode('utf-8'))
                    request = Request(**request_data)

                    # Skip the message if event_timestamp is older than PROCESS_REQUESTS_WITHIN_SECONDS.
                    if request.event_timestamp < time.time() - PROCESS_REQUESTS_WITHIN_SECONDS or not request.is_dishes_valid():
                        logger.debug(f"Skipping message with timestamp: {request.event_timestamp}")
                        self.consumer.commit(msg)
                        continue

                    with self.lock:
                        self.requests.append((msg, request))

                except (json.JSONDecodeError, TypeError) as e:
                    logger.error(f"Failed to parse message: {e}")
                    # TODO: Retry.
                    self.consumer.commit(msg)

            except Exception as e:
                logger.error(f"Error in consumer thread: {e}")

        logger.info("RealtimeProcessor stopped consuming messages...")

    def _process_requests(self):
        """Process requests from the queue."""
        logger.info("RealtimeProcessor started processing requests...")

        while self.running.is_set():
            try:
                with self.lock:
                    if not self.requests:
                        continue
                    message, request = self.requests.popleft()

                # Try to acquire lock for this request to current instance.
                if not redis_facade.acquire_lock(request.request_id):
                    logger.debug(f"Failed to acquire lock for request_id: {request.request_id}")
                    # Prefill for future clustering.
                    self.address_clusterer.add_address_to_address_df(request)

                    self.consumer.commit(message)
                    continue

                try:
                    result = self.address_clusterer.cluster_new_request(request)

                    # Notify observers about the result
                    self.notify_observers((request, result))

                finally:
                    # Release the lock
                    redis_facade.release_lock(request.request_id)
                    # Commit the message
                    self.consumer.commit(message)

            except Exception as e:
                logger.error(f"Error processing request: {e}")

        logger.info("RealtimeProcessor stopped processing requests...")

    def _reinit_consumer(self):
        """Reinitialize the Kafka consumer in case of fatal errors."""
        try:
            self.consumer.close()
            self.consumer.subscribe([self.filtered_order_topic])
            logger.info("Successfully reinitialized Kafka consumer")
        except Exception as e:
            logger.error(f"Failed to reinitialize consumer: {e}")

    def start(self):
        logger.info("Starting RealtimeProcessor...")
        self.running.set()
        self.consumer_thread.start()
        self.processor_thread.start()

        self.consumer_thread.join()
        self.processor_thread.join()

    def stop(self):
        logger.info("Stopping RealtimeProcessor...")
        self.running.clear()

        # Wait for threads to finish
        self.consumer_thread.join()
        self.processor_thread.join()

        # Close consumer
        self.consumer.close()
        logger.info("RealtimeProcessor stopped successfully")

    def add_observer(self, observer: Exporter):
        """Add an observer to be notified of clustering results."""
        if observer not in self.observers:
            self.observers.append(observer)

    def remove_observer(self, observer: Exporter):
        """Remove an observer."""
        if observer in self.observers:
            self.observers.remove(observer)

    def notify_observers(self, data: any):
        """Notify all observers with clustering results."""
        for observer in self.observers:
            try:
                observer.export(data)
            except Exception as e:
                logger.error(f"Error notifying observer: {e}")
