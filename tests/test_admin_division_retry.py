import json
import pytest
from unittest.mock import Mock, patch, MagicMock
import pandas as pd

from src.processors.address_parser import NERAddressParser, _has_missing_admin_divisions
from src.facades.address_map_call import extract_address_ner, _has_missing_admin_divisions_in_row
from src.data_structures.parsed_address import ParsedAddress


class TestAdminDivisionRetryLogic:
    """Test suite for admin division retry logic"""

    def test_has_missing_admin_divisions_with_all_empty(self):
        """Test that function correctly identifies when all admin divisions are missing"""
        parsed_address = ParsedAddress(
            address="Test address",
            formatted_address="Test formatted",
            freetext_formatted_address="Test freetext",
            state="",
            city="",
            district=""
        )
        assert _has_missing_admin_divisions(parsed_address) is True

    def test_has_missing_admin_divisions_with_state_only(self):
        """Test that function returns False when state is present"""
        parsed_address = ParsedAddress(
            address="Test address",
            formatted_address="Test formatted",
            freetext_formatted_address="Test freetext",
            state="Ho Chi Minh City",
            city="",
            district=""
        )
        assert _has_missing_admin_divisions(parsed_address) is False

    def test_has_missing_admin_divisions_with_city_only(self):
        """Test that function returns False when city is present"""
        parsed_address = ParsedAddress(
            address="Test address",
            formatted_address="Test formatted",
            freetext_formatted_address="Test freetext",
            state="",
            city="District 1",
            district=""
        )
        assert _has_missing_admin_divisions(parsed_address) is False

    def test_has_missing_admin_divisions_with_district_only(self):
        """Test that function returns False when district is present"""
        parsed_address = ParsedAddress(
            address="Test address",
            formatted_address="Test formatted",
            freetext_formatted_address="Test freetext",
            state="",
            city="",
            district="Ward 22"
        )
        assert _has_missing_admin_divisions(parsed_address) is False

    def test_has_missing_admin_divisions_with_all_present(self):
        """Test that function returns False when all admin divisions are present"""
        parsed_address = ParsedAddress(
            address="Test address",
            formatted_address="Test formatted",
            freetext_formatted_address="Test freetext",
            state="Ho Chi Minh City",
            city="District 1",
            district="Ward 22"
        )
        assert _has_missing_admin_divisions(parsed_address) is False

    def test_has_missing_admin_divisions_with_none(self):
        """Test that function returns True when parsed_address is None"""
        assert _has_missing_admin_divisions(None) is True

    def test_has_missing_admin_divisions_in_row_with_empty_segments(self):
        """Test row-based function with empty address segments"""
        row = {
            'buyer_shipping_address': 'Test address',
            'address_segment': '{"admin_division_1": "", "admin_division_2": "", "admin_division_3": ""}'
        }
        assert _has_missing_admin_divisions_in_row(row) is True

    def test_has_missing_admin_divisions_in_row_with_state_present(self):
        """Test row-based function with state present"""
        row = {
            'buyer_shipping_address': 'Test address',
            'address_segment': '{"admin_division_1": "Ho Chi Minh City", "admin_division_2": "", "admin_division_3": ""}'
        }
        assert _has_missing_admin_divisions_in_row(row) is False

    def test_has_missing_admin_divisions_in_row_with_invalid_json(self):
        """Test row-based function with invalid JSON"""
        row = {
            'buyer_shipping_address': 'Test address',
            'address_segment': 'invalid json'
        }
        assert _has_missing_admin_divisions_in_row(row) is True

    def test_has_missing_admin_divisions_in_row_with_missing_segment(self):
        """Test row-based function with missing address_segment field"""
        row = {
            'buyer_shipping_address': 'Test address'
        }
        assert _has_missing_admin_divisions_in_row(row) is True

    def test_has_missing_admin_divisions_in_row_with_empty_segment(self):
        """Test row-based function with empty address_segment"""
        row = {
            'buyer_shipping_address': 'Test address',
            'address_segment': ''
        }
        assert _has_missing_admin_divisions_in_row(row) is True


class TestNERAddressParserRetry:
    """Test suite for NERAddressParser retry logic"""

    @patch('src.facades.address_map_call.call_address_ner_batch')
    def test_parse_retries_on_missing_admin_divisions(self, mock_call_batch):
        """Test that parse method retries when admin divisions are missing"""
        parser = NERAddressParser('VN')
        
        # Mock responses: first two calls return empty admin divisions, third succeeds
        empty_response = pd.DataFrame({
            'freetext_formatted_address': ['Test formatted'],
            'formatted_address': ['Test address'],
            'address_segment': ['{"admin_division_1": "", "admin_division_2": "", "admin_division_3": ""}']
        })
        
        success_response = pd.DataFrame({
            'freetext_formatted_address': ['Test formatted'],
            'formatted_address': ['Test address'],
            'address_segment': ['{"admin_division_1": "Ho Chi Minh City", "admin_division_2": "District 1", "admin_division_3": "Ward 22"}']
        })
        
        mock_call_batch.side_effect = [empty_response, empty_response, success_response]
        
        result = parser.parse("Test address")
        
        # Should have been called 3 times due to retries
        assert mock_call_batch.call_count == 3
        assert result is not None
        assert result.state == "Ho Chi Minh City"
        assert result.city == "District 1"
        assert result.district == "Ward 22"

    @patch('src.facades.address_map_call.call_address_ner_batch')
    def test_parse_gives_up_after_max_retries(self, mock_call_batch):
        """Test that parse method gives up after maximum retries"""
        parser = NERAddressParser('VN')
        
        # Mock response that always returns empty admin divisions
        empty_response = pd.DataFrame({
            'freetext_formatted_address': ['Test formatted'],
            'formatted_address': ['Test address'],
            'address_segment': ['{"admin_division_1": "", "admin_division_2": "", "admin_division_3": ""}']
        })
        
        mock_call_batch.return_value = empty_response
        
        result = parser.parse("Test address")
        
        # Should have been called 3 times (max retries)
        assert mock_call_batch.call_count == 3
        assert result is not None
        assert result.state == ""
        assert result.city == ""
        assert result.district == ""

    @patch('src.facades.address_map_call.call_address_ner_batch')
    def test_parse_no_retry_when_admin_divisions_present(self, mock_call_batch):
        """Test that parse method doesn't retry when admin divisions are present"""
        parser = NERAddressParser('VN')
        
        # Mock response with admin divisions present
        success_response = pd.DataFrame({
            'freetext_formatted_address': ['Test formatted'],
            'formatted_address': ['Test address'],
            'address_segment': ['{"admin_division_1": "Ho Chi Minh City", "admin_division_2": "", "admin_division_3": ""}']
        })
        
        mock_call_batch.return_value = success_response
        
        result = parser.parse("Test address")
        
        # Should only be called once since admin divisions are present
        assert mock_call_batch.call_count == 1
        assert result is not None
        assert result.state == "Ho Chi Minh City"


class TestExtractAddressNERRetry:
    """Test suite for extract_address_ner retry logic"""

    @patch('src.facades.address_map_call.ner_api_call')
    def test_extract_address_ner_retries_on_missing_admin_divisions(self, mock_api_call):
        """Test that extract_address_ner retries when admin divisions are missing"""
        
        # Mock API responses: first two return empty admin divisions, third succeeds
        empty_entities = {
            'entities': [
                {'type': 'street', 'target_value': 'Test Street'}
            ],
            'partial_formatted_address': 'Test Street',
            'formatted_address': 'Test Street, Vietnam'
        }
        
        success_entities = {
            'entities': [
                {'type': 'admin_division_1', 'target_value': 'Ho Chi Minh City'},
                {'type': 'admin_division_2', 'target_value': 'District 1'},
                {'type': 'admin_division_3', 'target_value': 'Ward 22'},
                {'type': 'street', 'target_value': 'Test Street'}
            ],
            'partial_formatted_address': 'Test Street',
            'formatted_address': 'Test Street, Ward 22, District 1, Ho Chi Minh City, Vietnam'
        }
        
        mock_api_call.side_effect = [empty_entities, empty_entities, success_entities]
        
        row = {
            'buyer_shipping_address': 'Test address',
            'buyer_shipping_address_state': '',
            'buyer_shipping_address_city': '',
            'buyer_shipping_address_district': ''
        }
        
        result = extract_address_ner(row, 'VN')
        
        # Should have been called 3 times due to retries
        assert mock_api_call.call_count == 3
        
        # Check that the result contains admin divisions
        address_segment = json.loads(result['address_segment'])
        assert address_segment['admin_division_1'] == 'Ho Chi Minh City'
        assert address_segment['admin_division_2'] == 'District 1'
        assert address_segment['admin_division_3'] == 'Ward 22'

    @patch('src.facades.address_map_call.ner_api_call')
    def test_extract_address_ner_gives_up_after_max_retries(self, mock_api_call):
        """Test that extract_address_ner gives up after maximum retries"""
        
        # Mock API response that always returns empty admin divisions
        empty_entities = {
            'entities': [
                {'type': 'street', 'target_value': 'Test Street'}
            ],
            'partial_formatted_address': 'Test Street',
            'formatted_address': 'Test Street, Vietnam'
        }
        
        mock_api_call.return_value = empty_entities
        
        row = {
            'buyer_shipping_address': 'Test address',
            'buyer_shipping_address_state': '',
            'buyer_shipping_address_city': '',
            'buyer_shipping_address_district': ''
        }
        
        result = extract_address_ner(row, 'VN')
        
        # Should have been called 3 times (max retries)
        assert mock_api_call.call_count == 3
        
        # Check that the result doesn't contain admin divisions
        address_segment = json.loads(result['address_segment'])
        assert 'admin_division_1' not in address_segment
        assert 'admin_division_2' not in address_segment
        assert 'admin_division_3' not in address_segment

    @patch('src.facades.address_map_call.ner_api_call')
    def test_extract_address_ner_no_retry_when_admin_divisions_present(self, mock_api_call):
        """Test that extract_address_ner doesn't retry when admin divisions are present"""
        
        # Mock API response with admin divisions present
        success_entities = {
            'entities': [
                {'type': 'admin_division_1', 'target_value': 'Ho Chi Minh City'},
                {'type': 'street', 'target_value': 'Test Street'}
            ],
            'partial_formatted_address': 'Test Street',
            'formatted_address': 'Test Street, Ho Chi Minh City, Vietnam'
        }
        
        mock_api_call.return_value = success_entities
        
        row = {
            'buyer_shipping_address': 'Test address',
            'buyer_shipping_address_state': '',
            'buyer_shipping_address_city': '',
            'buyer_shipping_address_district': ''
        }
        
        result = extract_address_ner(row, 'VN')
        
        # Should only be called once since admin divisions are present
        assert mock_api_call.call_count == 1
        
        # Check that the result contains admin divisions
        address_segment = json.loads(result['address_segment'])
        assert address_segment['admin_division_1'] == 'Ho Chi Minh City'
